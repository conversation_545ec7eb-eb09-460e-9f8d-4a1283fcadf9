/**
 * 增强安全验证器
 *
 * 用于防止 SQL 注入、命令注入和其他安全漏洞的综合安全验证系统。
 * 实现多层保护，包括模式匹配、输入清理和编码验证。
 *
 * @fileoverview MySQL 操作的高级安全验证
 * <AUTHOR> MCP 团队
 * @since 1.0.0
 */

import { DefaultConfig, StringConstants } from './constants.js';

/**
 * 增强安全验证器类
 *
 * 多层安全验证系统，防护以下攻击：
 * - SQL 注入攻击
 * - 命令注入尝试
 * - 文件系统访问漏洞
 * - 信息泄露攻击
 * - 时序攻击和 DoS 尝试
 *
 * 安全特性：
 * - 预编译的正则表达式模式以提高性能
 * - 多种验证级别（严格、中等、基础）
 * - 字符编码验证
 * - 控制字符检测
 * - 长度限制强制执行
 *
 * @class EnhancedSecurityValidator
 * @since 1.0.0
 */
export class EnhancedSecurityValidator {
  /**
   * 危险 SQL 模式
   *
   * 预编译的正则表达式模式，检测可能危害系统安全或数据完整性的
   * 潜在危险 SQL 操作：
   *
   * - LOAD_FILE、INTO OUTFILE、INTO DUMPFILE：文件系统访问
   * - SYSTEM、EXEC、SHELL、xp_cmdshell：命令执行
   * - UNION SELECT 与 INFORMATION_SCHEMA：信息泄露
   * - 带 DDL 操作的堆叠查询：模式操作
   * - BENCHMARK、SLEEP、WAITFOR：时序攻击和 DoS
   * - 系统变量访问：配置泄露
   */
  private static DANGEROUS_PATTERNS: RegExp[] = [
    /\b(LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE)\b/i,
    /\b(SYSTEM|EXEC|SHELL|xp_cmdshell)\b/i,
    /\b(UNION\s+SELECT).*(\bFROM\s+INFORMATION_SCHEMA)\b/i,
    /;\s*(DROP|DELETE|TRUNCATE|ALTER)\b/i,
    /\b(BENCHMARK|SLEEP|WAITFOR)\s*\(/i,
    /@@(version|datadir|basedir|tmpdir)/i,
  ];

  /**
   * SQL 注入模式
   *
   * 预编译的正则表达式模式，检测常见的 SQL 注入技术：
   *
   * - 基于引号的注入与 OR/AND 条件
   * - 基于联合的注入尝试
   * - 基于布尔的盲注
   * - 带比较运算符的数字注入
   * - 经典的身份验证绕过模式
   */
  private static INJECTION_PATTERNS: RegExp[] = [
    /(\s|^)('|")\s*(OR|AND)\s*(\d+|'[^']*'|\")[^><=!]*(\s)*[><=!]{1,2}.*/i,
    /('|").*(\s|^)(UNION|SELECT|INSERT|DELETE|UPDATE|DROP|CREATE|ALTER)(\s)/i,
    /\s*(OR|AND)\s+[\w'"]+\s*[><=!]+.*/i,
    /('\s*OR\s*'\d+'\s*=\s*'\d')/i,
    /("\s*OR\s*"\d+"\s*=\s*"\d")/i,
  ];

  /**
   * Comprehensive Input Validation
   *
   * Main validation entry point that performs type checking and delegates
   * to specialized validators based on input type. Supports multiple
   * validation levels for different security requirements.
   *
   * Validation Levels:
   * - "strict": Full security validation with all patterns
   * - "moderate": Essential security checks only
   * - "basic": Minimal validation for performance-critical paths
   *
   * @public
   * @static
   * @param {any} inputValue - Value to validate (any type)
   * @param {string} fieldName - Name of the field for error messages
   * @param {string} [validationLevel="strict"] - Validation strictness level
   * @throws {Error} When input fails validation checks
   *
   * @example
   * // 严格验证（默认）
   * EnhancedSecurityValidator.validateInputComprehensive(userInput, "username");
   *
   * @example
   * // 为性能考虑的中等验证
   * EnhancedSecurityValidator.validateInputComprehensive(data, "field", "moderate");
   */
  public static validateInputComprehensive(inputValue: any, fieldName: string, validationLevel: string = "strict"): void {
    // 类型验证：确保输入是可接受的类型
    if (typeof inputValue !== 'string' && typeof inputValue !== 'number' && typeof inputValue !== 'boolean' && inputValue !== null && inputValue !== undefined) {
      throw new Error(`${fieldName} has invalid data type`);
    }

    // 字符串特定验证（最安全关键）
    if (typeof inputValue === 'string') {
      this.validateStringComprehensive(inputValue, fieldName, validationLevel);
    }
  }

  /**
   * Enhanced String Validation
   *
   * Comprehensive string validation including control character detection,
   * length limits, encoding validation, and security pattern matching.
   * Implements multiple security layers to prevent various attack vectors.
   *
   * Security Checks:
   * 1. Control character detection (prevents binary injection)
   * 2. Length limit enforcement (prevents buffer overflow)
   * 3. Character encoding validation (prevents encoding attacks)
   * 4. Dangerous pattern detection (prevents SQL injection)
   * 5. Injection pattern matching (prevents various injection types)
   *
   * @private
   * @static
   * @param {string} value - String value to validate
   * @param {string} fieldName - Field name for error messages
   * @param {string} level - Validation level ("strict", "moderate", "basic")
   * @throws {Error} When string fails any validation check
   */
  private static validateStringComprehensive(value: string, fieldName: string, level: string): void {
    // 控制字符验证（安全：防止二进制注入）
    if (value.split('').some(c => c.charCodeAt(0) < 32 && !['\t', '\n', '\r'].includes(c))) {
      throw new Error(`${fieldName} 包含无效的控制字符`);
    }

    // 长度验证（安全：防止缓冲区溢出攻击）
    if (value.length > DefaultConfig.MAX_INPUT_LENGTH) {
      throw new Error(`${fieldName} 超过最大长度限制`);
    }

    // 字符编码验证（安全：防止编码攻击）
    try {
      Buffer.from(value, 'utf-8');
    } catch (e) {
      throw new Error(`${fieldName} 包含无效的字符编码`);
    }

    // 基于模式的安全验证
    if (level === "strict") {
      // 完整安全验证：检查所有危险模式
      if (this.DANGEROUS_PATTERNS.some(pattern => pattern.test(value))) {
        throw new Error(`${fieldName} contains potentially dangerous content`);
      }

      // SQL 注入模式检测
      if (this.INJECTION_PATTERNS.some(pattern => pattern.test(value))) {
        throw new Error(`${fieldName} contains potential SQL injection attempt`);
      }
    } else if (level === "moderate") {
      // 中等验证：仅检查最关键的模式
      const criticalPatterns = this.DANGEROUS_PATTERNS.slice(0, 3);
      if (criticalPatterns.some(pattern => pattern.test(value))) {
        throw new Error(`${fieldName} contains dangerous content`);
      }
    }
    // "basic" 级别：为性能跳过模式验证
  }
}