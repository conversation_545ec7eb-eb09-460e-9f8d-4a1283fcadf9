/**
 * MySQL 管理器 - 主集成类
 *
 * 综合的 MySQL 管理系统，集成了连接池、缓存、安全验证、
 * 速率限制和性能监控功能。为所有数据库操作提供统一接口，
 * 具备企业级的可靠性和安全特性。
 *
 * @fileoverview 集成组件的核心 MySQL 管理类
 * <AUTHOR> MCP 团队
 * @since 1.0.0
 */

import { randomUUID } from 'crypto';
import { ConnectionPool } from './connection.js';
import { ConfigurationManager, DatabaseConfig, SecurityConfig, CacheConfig } from './config.js';
import { SmartCache } from './cache.js';
import { EnhancedSecurityValidator } from './security.js';
import { AdaptiveRateLimiter } from './rateLimit.js';
import { EnhancedMetricsManager, PerformanceMetrics } from './metrics.js';
import { StringConstants, DefaultConfig, MySQLErrorCodes } from './constants.js';
import { PoolConnection } from 'mysql2/promise';

/**
 * 重试策略接口
 *
 * 定义实现重试机制的契约，具有可配置的退避策略
 * 用于处理瞬态数据库错误。
 */
interface RetryStrategy {
  /** 放弃前的最大重试次数 */
  maxAttempts: number;
  /** 第一次重试的基础延迟（秒） */
  baseDelay: number;
  /** 防止过度等待的最大延迟（秒） */
  maxDelay: number;
  /** 每次重试尝试的指数退避乘数 */
  backoffFactor: number;

  /**
   * 计算特定重试尝试的延迟
   * @param attempt - 当前尝试次数（从0开始）
   * @returns 下次重试前的延迟秒数
   */
  getDelay(attempt: number): number;
}

/**
 * 默认重试策略实现
 *
 * 实现具有可配置参数的指数退避算法。
 * 为大多数数据库重试场景提供合理的默认值。
 */
class DefaultRetryStrategy implements RetryStrategy {
  maxAttempts: number = DefaultConfig.MAX_RETRY_ATTEMPTS;
  baseDelay: number = 1.0;
  maxDelay: number = 10.0;
  backoffFactor: number = 2.0;

  /**
   * 计算带有最大上限的指数退避延迟
   * @param attempt - 当前重试尝试次数
   * @returns 计算出的延迟秒数
   */
  getDelay(attempt: number): number {
    const delay = this.baseDelay * Math.pow(this.backoffFactor, attempt);
    return Math.min(delay, this.maxDelay);
  }
}

/**
 * MySQL 管理器类
 *
 * 中央管理类，协调所有 MySQL 操作，集成了安全性、
 * 性能监控、缓存和连接管理功能。
 *
 * 功能特性：
 * - 带健康监控的连接池
 * - 多级缓存（模式、表存在性、索引）
 * - 安全验证和 SQL 注入防护
 * - 基于系统负载的自适应速率限制
 * - 全面的性能指标和告警
 * - 指数退避的自动重试
 * - 优雅的错误处理和恢复
 *
 * @class MySQLManager
 * @since 1.0.0
 */
export class MySQLManager {
  /** 用于跟踪和调试的唯一会话标识符 */
  private sessionId: string;

  /** 数据库、安全和缓存设置的配置管理器 */
  private configManager: ConfigurationManager;

  /** 高效数据库连接的连接池管理器 */
  private connectionPool: ConnectionPool;

  /** 表模式信息的智能缓存 */
  private schemaCache: SmartCache<any>;

  /** 表存在性检查的智能缓存 */
  private tableExistsCache: SmartCache<boolean>;

  /** 索引信息的智能缓存 */
  private indexCache: SmartCache<any>;

  /** 传统性能指标收集器 */
  private metrics: PerformanceMetrics;

  /** 带时间序列数据和告警的增强指标管理器 */
  private enhancedMetrics: EnhancedMetricsManager;

  /** 用于输入清理和 SQL 注入防护的安全验证器 */
  private securityValidator: EnhancedSecurityValidator;

  /** 请求节流的自适应速率限制器 */
  private adaptiveRateLimiter: AdaptiveRateLimiter;

  /** 处理瞬态错误的重试策略 */
  private retryStrategy: RetryStrategy;

  /**
   * Pre-compiled dangerous SQL patterns for security validation
   * These patterns detect potentially harmful SQL operations that could
   * compromise system security or data integrity.
   */
  private static DANGEROUS_PATTERNS: RegExp[] = [
    /\b(LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE)\b/i,
    /\b(SYSTEM|EXEC|SHELL)\b/i,
    /\bINTO\s+OUTFILE\b/i,
    /\bLOAD\s+DATA\b/i,
  ];

  /**
   * Table name validation pattern
   * Ensures table names contain only safe characters (alphanumeric, underscore, hyphen)
   */
  private static TABLE_NAME_PATTERN: RegExp = /^[a-zA-Z0-9_-]+$/;
  
  /**
   * MySQL Manager Constructor
   *
   * Initializes all components of the MySQL management system including
   * configuration, connection pooling, caching, security, and monitoring.
   *
   * @constructor
   * @throws {Error} When component initialization fails
   */
  constructor() {
    // 生成用于跟踪的唯一会话标识符
    this.sessionId = randomUUID();

    // 初始化集中配置管理
    this.configManager = new ConfigurationManager();

    // 使用数据库配置初始化连接池
    this.connectionPool = new ConnectionPool(this.configManager.database);

    // 使用配置的大小和 TTL 初始化智能缓存系统
    this.schemaCache = new SmartCache(
      this.configManager.cache.schemaCacheSize,
      this.configManager.cache.cacheTTL
    );
    this.tableExistsCache = new SmartCache(
      this.configManager.cache.tableExistsCacheSize,
      this.configManager.cache.cacheTTL
    );
    this.indexCache = new SmartCache(
      this.configManager.cache.indexCacheSize,
      this.configManager.cache.cacheTTL
    );

    // 初始化性能监控系统
    this.metrics = new PerformanceMetrics();
    this.enhancedMetrics = new EnhancedMetricsManager();
    this.enhancedMetrics.startMonitoring();

    // 初始化安全验证系统
    this.securityValidator = new EnhancedSecurityValidator();

    // 使用安全配置初始化自适应速率限制
    this.adaptiveRateLimiter = new AdaptiveRateLimiter(
      this.configManager.security.rateLimitMax,
      this.configManager.security.rateLimitWindow
    );

    // 初始化错误处理的重试策略
    this.retryStrategy = new DefaultRetryStrategy();
  }
  
  /**
   * Execute Operation with Retry Mechanism
   *
   * Executes database operations with automatic retry using exponential backoff.
   * Handles transient errors while avoiding retries for permanent failures.
   *
   * @private
   * @template T - Return type of the operation
   * @param operation - Async operation to execute with retry logic
   * @returns Promise resolving to operation result
   * @throws {Error} When all retry attempts are exhausted or non-retryable error occurs
   */
  private async executeWithRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < this.retryStrategy.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;

        // 跳过某些不会通过重试解决的错误类型
        if (error.code) {
          const errorCode = parseInt(error.code, 10);
          if (errorCode === MySQLErrorCodes.ACCESS_DENIED ||
              errorCode === MySQLErrorCodes.PARSE_ERROR) {
            break;
          }
        }

        // 在下次尝试前应用指数退避延迟
        if (attempt < this.retryStrategy.maxAttempts - 1) {
          const delay = this.retryStrategy.getDelay(attempt) * 1000; // 转换为毫秒
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError || new Error("Operation failed after all retries");
  }

  /**
   * Validate Input Data
   *
   * Performs comprehensive security validation on input data to prevent
   * SQL injection and other security vulnerabilities.
   *
   * @private
   * @param inputValue - Value to validate (string, number, boolean, null, undefined)
   * @param fieldName - Name of the field being validated (for error messages)
   * @param validationLevel - Validation strictness level ("strict", "moderate", "basic")
   * @throws {Error} When input fails security validation
   */
  private validateInput(inputValue: any, fieldName: string, validationLevel: string = "strict"): void {
    EnhancedSecurityValidator.validateInputComprehensive(inputValue, fieldName, validationLevel);
  }
  
  /**
   * Validate SQL Query Security
   *
   * Performs comprehensive security validation on SQL queries including
   * length limits, dangerous pattern detection, and query type restrictions.
   *
   * @private
   * @param query - SQL query string to validate
   * @throws {Error} When query fails security validation
   */
  private validateQuery(query: string): void {
    // 检查查询长度是否超过配置的最大值
    if (query.length > this.configManager.security.maxQueryLength) {
      throw new Error(StringConstants.MSG_QUERY_TOO_LONG);
    }

    // 扫描可能危害安全的危险 SQL 模式
    for (const pattern of MySQLManager.DANGEROUS_PATTERNS) {
      if (pattern.test(query)) {
        throw new Error(StringConstants.MSG_PROHIBITED_OPERATIONS);
      }
    }

    // 提取并验证查询类型（第一个单词）
    const firstWordEnd = query.indexOf(' ');
    const queryType = (firstWordEnd !== -1 ? query.substring(0, firstWordEnd) : query).trim().toUpperCase();

    // 确保查询类型在允许列表中
    if (!this.configManager.security.allowedQueryTypes.includes(queryType)) {
      throw new Error(StringConstants.MSG_QUERY_TYPE_NOT_ALLOWED.replace('{query_type}', queryType));
    }
  }

  /**
   * Validate Table Name
   *
   * Validates table names against security patterns and length restrictions
   * to prevent SQL injection and ensure compatibility.
   *
   * @private
   * @param tableName - Table name to validate
   * @throws {Error} When table name is invalid or too long
   */
  private validateTableName(tableName: string): void {
    // 检查是否符合允许的字符模式
    if (!MySQLManager.TABLE_NAME_PATTERN.test(tableName)) {
      throw new Error(StringConstants.MSG_INVALID_TABLE_NAME);
    }

    // 检查长度限制
    if (tableName.length > DefaultConfig.MAX_TABLE_NAME_LENGTH) {
      throw new Error(StringConstants.MSG_TABLE_NAME_TOO_LONG);
    }
  }

  /**
   * Check Rate Limit
   *
   * Verifies that the current request is within rate limiting bounds
   * using the adaptive rate limiter.
   *
   * @private
   * @param identifier - Unique identifier for rate limiting (defaults to "default")
   * @throws {Error} When rate limit is exceeded
   */
  private checkRateLimit(identifier: string = "default"): void {
    if (!this.adaptiveRateLimiter.checkRateLimit(identifier)) {
      throw new Error(StringConstants.MSG_RATE_LIMIT_EXCEEDED);
    }
  }
  
  /**
   * Get Table Schema with Caching
   *
   * Retrieves table schema information with intelligent caching to improve
   * performance. Cache misses trigger database queries while hits return
   * cached data immediately.
   *
   * @private
   * @param tableName - Name of the table to get schema for
   * @returns Promise resolving to table schema information
   * @throws {Error} When schema query fails
   */
  private async getTableSchemaCached(tableName: string): Promise<any> {
    const cacheKey = `schema_${tableName}`;
    let result = this.schemaCache.get(cacheKey);

    if (result === null) {
      // 缓存未命中：执行模式查询
      const schemaQuery = `
        SELECT
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          COLUMN_KEY,
          EXTRA,
          COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
        ORDER BY ORDINAL_POSITION
      `;

      result = await this.executeQuery(schemaQuery, [tableName]);
      this.schemaCache.put(cacheKey, result);
      this.metrics.cacheMisses++;
    } else {
      // 缓存命中：返回缓存数据
      this.metrics.cacheHits++;
    }

    return result ?? false;
  }

  /**
   * Check Table Existence with Caching
   *
   * Verifies if a table exists in the current database with caching
   * to avoid repeated INFORMATION_SCHEMA queries.
   *
   * @private
   * @param tableName - Name of the table to check
   * @returns Promise resolving to true if table exists, false otherwise
   * @throws {Error} When existence check query fails
   */
  private async tableExistsCached(tableName: string): Promise<boolean> {
    const cacheKey = `exists_${tableName}`;
    let result = this.tableExistsCache.get(cacheKey);

    if (result === null) {
      // 缓存未命中：执行存在性检查查询
      const existsQuery = `
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
      `;

      const queryResult = await this.executeQuery(existsQuery, [tableName]);
      result = queryResult && queryResult[0] && queryResult[0].count > 0;
      this.tableExistsCache.put(cacheKey, result ?? false);
      this.metrics.cacheMisses++;
    } else {
      // 缓存命中：返回缓存结果
      this.metrics.cacheHits++;
    }

    return result ?? false;
  }
  
  /**
   * Execute SQL Query
   *
   * Main public method for executing SQL queries with comprehensive security,
   * performance monitoring, caching, and error handling. Includes rate limiting,
   * retry mechanisms, and metrics collection.
   *
   * @public
   * @param query - SQL query string to execute
   * @param params - Optional parameters for prepared statements
   * @returns Promise resolving to query results
   * @throws {Error} When rate limit exceeded, security validation fails, or query execution fails
   *
   * @example
   * // 简单查询
   * const results = await manager.executeQuery("SELECT * FROM users LIMIT 10");
   *
   * @example
   * // 参数化查询
   * const user = await manager.executeQuery("SELECT * FROM users WHERE id = ?", [123]);
   */
  public async executeQuery(query: string, params?: any[]): Promise<any> {
    const startTime = Date.now();

    try {
      // 应用速率限制以防止滥用
      this.checkRateLimit();

      // 验证查询的安全合规性
      this.validateQuery(query);

      // 在瞬态故障时自动重试执行
      const result = await this.executeWithRetry(async () => {
        return await this.executeQueryInternal(query, params);
      });

      // 记录成功执行的指标
      const queryTime = (Date.now() - startTime) / 1000; // 转换为秒
      const isSlow = queryTime > DefaultConfig.SLOW_QUERY_THRESHOLD;
      this.updateMetrics(queryTime, false, isSlow);

      return result;
    } catch (error) {
      // 记录错误指标
      const queryTime = (Date.now() - startTime) / 1000; // 转换为秒
      this.updateMetrics(queryTime, true, false);
      throw error;
    }
  }

  /**
   * Internal Query Execution
   *
   * Low-level method that handles the actual database connection and query execution.
   * Manages connection lifecycle and ensures proper resource cleanup.
   *
   * @private
   * @param query - SQL query string
   * @param params - Optional query parameters
   * @returns Promise resolving to raw query results
   * @throws {Error} When connection or query execution fails
   */
  private async executeQueryInternal(query: string, params?: any[]): Promise<any> {
    const connection = await this.connectionPool.getConnection();

    try {
      const [rows] = await connection.execute(query, params);
      return rows;
    } finally {
      // 始终将连接释放回连接池
      connection.release();
    }
  }
  
  // 更新性能指标
  private updateMetrics(queryTime: number, isError: boolean = false, isSlow: boolean = false): void {
    this.metrics.queryCount++;
    this.metrics.totalQueryTime += queryTime;
    
    if (isError) {
      this.metrics.errorCount++;
      this.enhancedMetrics.recordError("query_error", "medium");
    }
    
    if (isSlow) {
      this.metrics.slowQueryCount++;
    }
    
    // 记录到增强指标管理器
    this.enhancedMetrics.recordQueryTime(queryTime);

    // 更新缓存命中率指标
    const cacheHitRate = this.metrics.getCacheHitRate();
    this.enhancedMetrics.recordCacheHitRate(cacheHitRate);
  }
  
  // 使缓存失效
  public invalidateCaches(operationType: string = "DDL", tableName?: string): void {
    if (operationType === "DDL" || operationType === "CREATE" || operationType === "DROP" || operationType === "ALTER") {
      // DDL 操作清除所有缓存
      this.clearAllCaches();
    } else if (operationType === "DML" && tableName) {
      // DML 操作清除特定表缓存
      this.invalidateTableSpecificCache(tableName);
    }
  }
  
  // 清除所有缓存
  private clearAllCaches(): void {
    this.schemaCache.clear();
    this.tableExistsCache.clear();
    this.indexCache.clear();
  }
  
  // 使特定表缓存失效
  private invalidateTableSpecificCache(tableName: string): void {
    const cacheKeysToRemove = [
      `schema_${tableName}`,
      `exists_${tableName}`,
      `indexes_${tableName}`
    ];
    
    // 从所有缓存中移除键
    cacheKeysToRemove.forEach(key => {
      // 我们无法直接访问缓存内部，所以只是清除它们
      // 在真实实现中，我们会有一个方法来移除特定键
    });
  }
  
  /**
   * Get Performance Metrics
   *
   * Retrieves comprehensive performance metrics including query statistics,
   * cache performance, and connection pool status for monitoring and debugging.
   *
   * @public
   * @returns Object containing detailed performance metrics
   *
   * @example
   * const metrics = manager.getPerformanceMetrics();
   * console.log(`Cache hit rate: ${metrics.cache_stats.schema_cache.hit_rate}`);
   */
  public getPerformanceMetrics(): Record<string, any> {
    return {
      [StringConstants.SECTION_PERFORMANCE]: this.metrics.toObject(),
      [StringConstants.SECTION_CACHE_STATS]: {
        [StringConstants.SECTION_SCHEMA_CACHE]: this.schemaCache.getStats(),
        [StringConstants.SECTION_TABLE_EXISTS_CACHE]: this.tableExistsCache.getStats(),
        [StringConstants.SECTION_INDEX_CACHE]: this.indexCache.getStats()
      },
      [StringConstants.SECTION_CONNECTION_POOL]: this.connectionPool.getStats()
    };
  }

  /**
   * Close MySQL Manager
   *
   * Performs graceful shutdown of all components including metrics monitoring,
   * connection pool closure, and cache cleanup. Should be called during
   * application shutdown to prevent resource leaks.
   *
   * @public
   * @returns Promise that resolves when all cleanup is complete
   *
   * @example
   * // 优雅关闭
   * await manager.close();
   */
  public async close(): Promise<void> {
    try {
      // 停止增强指标监控
      this.enhancedMetrics.stopMonitoring();

      // 关闭连接池并释放所有连接
      await this.connectionPool.close();

      // 清除所有缓存以释放内存
      this.schemaCache.clear();
      this.tableExistsCache.clear();
      this.indexCache.clear();
    } catch (error) {
      console.error(`${StringConstants.MSG_ERROR_DURING_CLEANUP} ${error}`);
    }
  }
}