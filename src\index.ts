/**
 * MySQL MCP 服务器实现
 *
 * 完整的 MySQL 数据库操作模型上下文协议 (MCP) 服务器。
 * 提供安全、高性能的 CRUD 操作、模式管理和连接诊断工具，
 * 内置缓存、速率限制和安全验证功能。
 *
 * @fileoverview MySQL MCP 服务器的主入口点
 * <AUTHOR> MCP 团队
 * @version 1.0.0
 * @since 1.0.0
 */

import { FastMCP } from 'fastmcp';
import { z } from 'zod';
import { MySQLManager } from './mysqlManager.js';
import { StringConstants } from './constants.js';

/**
 * 全局 MySQL 连接管理器实例
 * 处理所有数据库操作，包括连接池、缓存和安全验证
 */
const mysqlManager = new MySQLManager();

/**
 * FastMCP 服务器实例配置
 * 使用常量中的服务器名称和版本进行配置
 */
const mcp = new FastMCP({
  name: StringConstants.SERVER_NAME,
  version: "1.0.0"
});

/**
 * MySQL 查询执行工具
 *
 * 执行任意 MySQL 查询，支持参数绑定以确保安全性。
 * 支持 SELECT、SHOW、DESCRIBE、INSERT、UPDATE、DELETE、CREATE、DROP 和 ALTER 操作。
 *
 * @tool mysql_query
 * @param {string} query - 要执行的 SQL 查询（最大长度由安全配置强制执行）
 * @param {any[]} [params] - 预处理语句的可选参数，用于防止 SQL 注入
 * @returns {Promise<string>} JSON 格式的查询结果
 * @throws {Error} 当查询验证失败、超出速率限制或发生数据库错误时抛出
 *
 * @example
 * // 简单的 SELECT 查询
 * mysql_query({ query: "SELECT * FROM users LIMIT 10" })
 *
 * @example
 * // 安全的参数化查询
 * mysql_query({
 *   query: "SELECT * FROM users WHERE id = ? AND status = ?",
 *   params: [123, "active"]
 * })
 */
mcp.addTool({
  name: 'mysql_query',
  description: 'Execute MySQL queries (SELECT, SHOW, DESCRIBE, etc.)',
  parameters: z.object({
    query: z.string().describe('SQL query to execute'),
    params: z.array(z.any()).optional().describe('Optional parameters for prepared statements')
  }),
  execute: async (args) => {
    try {
      // 如果未提供参数，则初始化参数数组
      if (!args.params) {
        args.params = [];
      }

      // 安全验证：验证查询和所有参数
      mysqlManager['validateInput'](args.query, "query");
      args.params.forEach((param, i) => {
        mysqlManager['validateInput'](param, `param_${i}`);
      });

      // 使用重试机制和性能监控执行查询
      const result = await mysqlManager.executeQuery(args.query, args.params);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_QUERY_FAILED} ${error.message}`);
    }
  }
});

/**
 * 显示表工具
 *
 * 使用 SHOW TABLES 命令列出当前数据库中的所有表。
 * 结果会被缓存以优化性能。
 *
 * @tool mysql_show_tables
 * @returns {Promise<string>} JSON 格式的表名列表
 * @throws {Error} 当数据库连接失败或查询执行错误时抛出
 *
 * @example
 * // 获取当前数据库中的所有表
 * mysql_show_tables({})
 */
mcp.addTool({
  name: 'mysql_show_tables',
  description: 'Show all tables in the current database',
  parameters: z.object({}),
  execute: async () => {
    try {
      const showTablesQuery = "SHOW TABLES";
      const result = await mysqlManager.executeQuery(showTablesQuery);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_SHOW_TABLES_FAILED} ${error.message}`);
    }
  }
});

/**
 * 描述表工具
 *
 * 检索并描述指定表的结构，包括列、数据类型、约束和其他元数据。
 * 结果会被缓存以提高性能。
 *
 * @tool mysql_describe_table
 * @param {string} table_name - 要描述的表名（经过安全验证）
 * @returns {Promise<string>} JSON 格式的表结构信息
 * @throws {Error} 当表名无效、表不存在或查询失败时抛出
 *
 * @example
 * // 描述特定表
 * mysql_describe_table({ table_name: "users" })
 */
mcp.addTool({
  name: 'mysql_describe_table',
  description: 'Describe the structure of a specified table',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to describe')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);
      const result = await mysqlManager['getTableSchemaCached'](args.table_name);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_DESCRIBE_TABLE_FAILED} ${error.message}`);
    }
  }
});

/**
 * 查询数据工具
 *
 * 从表中查询数据，支持可选的过滤、列选择和行数限制。
 * 支持灵活的查询，具有安全验证和性能优化功能。
 *
 * @tool mysql_select_data
 * @param {string} table_name - 要查询数据的表名（经过安全验证）
 * @param {string[]} [columns] - 可选的列名列表（默认为所有列）
 * @param {string} [where_clause] - 可选的 WHERE 子句用于过滤（不包含 WHERE 关键字）
 * @param {number} [limit] - 可选的返回行数限制（用于性能优化）
 * @returns {Promise<string>} JSON 格式的查询结果
 * @throws {Error} 当表名无效、列不存在或查询失败时抛出
 *
 * @example
 * // 查询表中的所有数据
 * mysql_select_data({ table_name: "users" })
 *
 * @example
 * // 查询特定列并进行过滤和限制
 * mysql_select_data({
 *   table_name: "users",
 *   columns: ["id", "name", "email"],
 *   where_clause: "status = 'active'",
 *   limit: 50
 * })
 */
mcp.addTool({
  name: 'mysql_select_data',
  description: 'Select data from a table with optional conditions and limits',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to select data from'),
    columns: z.array(z.string()).optional().describe('Optional list of column names to select'),
    where_clause: z.string().optional().describe('Optional WHERE clause for filtering'),
    limit: z.number().int().optional().describe('Optional limit on number of rows returned')
  }),
  execute: async (args) => {
    try {
      // 验证表名的安全性
      mysqlManager['validateTableName'](args.table_name);

      // 如果未指定列，则默认为所有列
      if (!args.columns) {
        args.columns = ["*"];
      }

      // 验证每个列名（通配符除外）
      args.columns.forEach(col => {
        if (col !== "*") {
          mysqlManager['validateInput'](col, "column");
        }
      });

      // 构建带有适当转义的 SELECT 查询
      let query = `SELECT ${args.columns.join(', ')} FROM \`${args.table_name}\``;

      // 如果提供了 WHERE 子句，则添加
      if (args.where_clause) {
        mysqlManager['validateInput'](args.where_clause, "where_clause");
        query += ` WHERE ${args.where_clause}`;
      }

      // 如果提供了 LIMIT 子句，则添加（确保整数值）
      if (args.limit) {
        query += ` LIMIT ${Math.floor(args.limit)}`;
      }

      const result = await mysqlManager.executeQuery(query);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_SELECT_DATA_FAILED} ${error.message}`);
    }
  }
});

/**
 * 插入数据工具
 *
 * 使用参数化查询安全地向表中插入新数据。
 * 验证所有输入数据并使用预处理语句防止 SQL 注入。
 *
 * @tool mysql_insert_data
 * @param {string} table_name - 要插入数据的表名（经过安全验证）
 * @param {Record<string, any>} data - 要插入的列名和值的键值对
 * @returns {Promise<string>} 包含成功状态和插入信息的 JSON 格式结果
 * @throws {Error} 当表名无效、列名无效或插入失败时抛出
 *
 * @example
 * // 插入新用户记录
 * mysql_insert_data({
 *   table_name: "users",
 *   data: {
 *     name: "张三",
 *     email: "<EMAIL>",
 *     status: "active"
 *   }
 * })
 */
mcp.addTool({
  name: 'mysql_insert_data',
  description: 'Insert new data into a table',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to insert data into'),
    data: z.record(z.any()).describe('Key-value pairs of column names and values')
  }),
  execute: async (args) => {
    try {
      // 验证表名的安全性
      mysqlManager['validateTableName'](args.table_name);

      // 验证所有列名和值
      Object.keys(args.data).forEach(key => {
        mysqlManager['validateInput'](key, "column_name");
        mysqlManager['validateInput'](args.data[key], "column_value");
      });

      // 准备参数化 INSERT 查询
      const columns = Object.keys(args.data);
      const values = Object.values(args.data);
      const placeholders = columns.map(() => "?").join(", ");

      const query = `INSERT INTO \`${args.table_name}\` (\`${columns.join('`, `')}\`) VALUES (${placeholders})`;
      const result = await mysqlManager.executeQuery(query, values);

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_INSERT_DATA_FAILED} ${error.message}`);
    }
  }
});

/**
 * 更新数据工具
 *
 * 使用参数化查询根据指定条件更新表中的现有数据。
 * 提供安全的数据修改，具有 WHERE 子句验证和预处理语句功能。
 *
 * @tool mysql_update_data
 * @param {string} table_name - 要更新的表名（经过安全验证）
 * @param {Record<string, any>} data - 列名和新值的键值对
 * @param {string} where_clause - 指定要更新记录的 WHERE 子句（不包含 WHERE 关键字）
 * @returns {Promise<string>} 包含成功状态和更新信息的 JSON 格式结果
 * @throws {Error} 当表名无效、WHERE 子句缺失/无效或更新失败时抛出
 *
 * @example
 * // 更新用户状态
 * mysql_update_data({
 *   table_name: "users",
 *   data: { status: "inactive", updated_at: "2024-01-01" },
 *   where_clause: "id = 123"
 * })
 */
mcp.addTool({
  name: 'mysql_update_data',
  description: 'Update existing data in a table based on specified conditions',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to update'),
    data: z.record(z.any()).describe('Key-value pairs of column names and new values'),
    where_clause: z.string().describe('WHERE clause to specify which records to update (without WHERE keyword)')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);
      mysqlManager['validateInput'](args.where_clause, "where_clause");

      Object.keys(args.data).forEach(key => {
        mysqlManager['validateInput'](key, "column_name");
        mysqlManager['validateInput'](args.data[key], "column_value");
      });

      const columns = Object.keys(args.data);
      const values = Object.values(args.data);
      const setClause = columns.map(col => `\`${col}\` = ?`).join(", ");

      const query = `UPDATE \`${args.table_name}\` SET ${setClause} WHERE ${args.where_clause}`;
      const result = await mysqlManager.executeQuery(query, values);

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_UPDATE_DATA_FAILED} ${error.message}`);
    }
  }
});

/**
 * 删除数据工具
 *
 * 根据指定条件从表中删除数据。
 * 使用安全的参数化查询和 WHERE 子句验证。
 *
 * @tool mysql_delete_data
 * @param {string} table_name - 要删除数据的表名（经过安全验证）
 * @param {string} where_clause - 指定要删除记录的 WHERE 子句（不包含 WHERE 关键字）
 * @returns {Promise<string>} 包含成功状态和删除信息的 JSON 格式结果
 * @throws {Error} 当表名无效、WHERE 子句无效或删除失败时抛出
 */
mcp.addTool({
  name: 'mysql_delete_data',
  description: 'Delete data from a table based on specified conditions',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to delete data from'),
    where_clause: z.string().describe('WHERE clause to specify which records to delete (without WHERE keyword)')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);
      mysqlManager['validateInput'](args.where_clause, "where_clause");

      const query = `DELETE FROM \`${args.table_name}\` WHERE ${args.where_clause}`;
      const result = await mysqlManager.executeQuery(query);

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_DELETE_DATA_FAILED} ${error.message}`);
    }
  }
});

/**
 * 获取数据库模式工具
 *
 * 检索数据库模式信息，包括表、列和约束。
 * 提供完整的数据库结构信息用于分析和管理。
 *
 * @tool mysql_get_schema
 * @param {string} [table_name] - 可选的特定表名，用于获取该表的模式信息
 * @returns {Promise<string>} 包含数据库模式信息的 JSON 格式结果
 * @throws {Error} 当查询失败或表名无效时抛出
 */
mcp.addTool({
  name: 'mysql_get_schema',
  description: 'Get database schema information including tables, columns, and constraints',
  parameters: z.object({
    table_name: z.string().optional().describe('Optional specific table name to get schema information for')
  }),
  execute: async (args) => {
    try {
      let query = `
        SELECT
          TABLE_NAME,
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          COLUMN_KEY,
          EXTRA,
          COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
      `;

      const params: string[] = [];
      if (args.table_name) {
        mysqlManager['validateTableName'](args.table_name);
        query += " AND TABLE_NAME = ?";
        params.push(args.table_name);
      }

      query += " ORDER BY TABLE_NAME, ORDINAL_POSITION";

      const result = await mysqlManager.executeQuery(query, params.length > 0 ? params : undefined);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_GET_SCHEMA_FAILED} ${error.message}`);
    }
  }
});

/**
 * 获取索引工具
 *
 * 检索特定表或数据库中所有表的索引信息。
 * 提供索引结构和性能优化相关的详细信息。
 *
 * @tool mysql_get_indexes
 * @param {string} [table_name] - 可选的特定表名，用于获取该表的索引信息
 * @returns {Promise<string>} 包含索引信息的 JSON 格式结果
 * @throws {Error} 当查询失败或表名无效时抛出
 */
mcp.addTool({
  name: 'mysql_get_indexes',
  description: 'Get index information for a specific table or all tables in the database',
  parameters: z.object({
    table_name: z.string().optional().describe('Optional specific table name to get index information for')
  }),
  execute: async (args) => {
    try {
      if (args.table_name) {
        mysqlManager['validateTableName'](args.table_name);
        // 使用缓存版本以获得更好的性能
        const cacheKey = `indexes_${args.table_name}`;
        let cachedResult = mysqlManager['indexCache'].get(cacheKey);

        if (cachedResult === null) {
          const indexesQuery = `
            SELECT
              INDEX_NAME,
              COLUMN_NAME,
              NON_UNIQUE,
              SEQ_IN_INDEX,
              INDEX_TYPE
            FROM INFORMATION_SCHEMA.STATISTICS
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
            ORDER BY INDEX_NAME, SEQ_IN_INDEX
          `;
          const queryResult = await mysqlManager.executeQuery(indexesQuery, [args.table_name]);
          const result = JSON.stringify(queryResult, null, 2);
          mysqlManager['indexCache'].put(cacheKey, result);
          mysqlManager['metrics'].cacheMisses++;
          return result;
        } else {
          mysqlManager['metrics'].cacheHits++;
          return cachedResult;
        }
      } else {
        // 获取所有表的索引信息（不使用缓存）
        const query = `
          SELECT
            TABLE_NAME,
            INDEX_NAME,
            COLUMN_NAME,
            NON_UNIQUE,
            SEQ_IN_INDEX,
            INDEX_TYPE
          FROM INFORMATION_SCHEMA.STATISTICS
          WHERE TABLE_SCHEMA = DATABASE()
          ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX
        `;
        const result = await mysqlManager.executeQuery(query);
        return JSON.stringify(result, null, 2);
      }
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_GET_INDEXES_FAILED} ${error.message}`);
    }
  }
});

/**
 * 获取外键工具
 *
 * 检索特定表或数据库中所有表的外键约束信息。
 * 提供表间关系和引用完整性约束的详细信息。
 *
 * @tool mysql_get_foreign_keys
 * @param {string} [table_name] - 可选的特定表名，用于获取该表的外键信息
 * @returns {Promise<string>} 包含外键约束信息的 JSON 格式结果
 * @throws {Error} 当查询失败或表名无效时抛出
 */
mcp.addTool({
  name: 'mysql_get_foreign_keys',
  description: 'Get foreign key constraint information for a specific table or all tables in the database',
  parameters: z.object({
    table_name: z.string().optional().describe('Optional specific table name to get foreign key information for')
  }),
  execute: async (args) => {
    try {
      let query = `
        SELECT
          TABLE_NAME,
          COLUMN_NAME,
          CONSTRAINT_NAME,
          REFERENCED_TABLE_NAME,
          REFERENCED_COLUMN_NAME,
          UPDATE_RULE,
          DELETE_RULE
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE TABLE_SCHEMA = DATABASE()
          AND REFERENCED_TABLE_NAME IS NOT NULL
      `;

      const params: string[] = [];
      if (args.table_name) {
        mysqlManager['validateTableName'](args.table_name);
        query += " AND TABLE_NAME = ?";
        params.push(args.table_name);
      }

      query += " ORDER BY TABLE_NAME, CONSTRAINT_NAME";

      const result = await mysqlManager.executeQuery(query, params.length > 0 ? params : undefined);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_GET_FOREIGN_KEYS_FAILED} ${error.message}`);
    }
  }
});

/**
 * 创建表工具
 *
 * 使用指定的列和约束创建新表。
 * 支持完整的表定义，包括主键、自增列和默认值。
 *
 * @tool mysql_create_table
 * @param {string} table_name - 要创建的表名（经过安全验证）
 * @param {Array} columns - 列定义数组，包含名称、类型、约束等信息
 * @returns {Promise<string>} 包含成功状态和创建信息的 JSON 格式结果
 * @throws {Error} 当表名无效、列定义错误或创建失败时抛出
 */
mcp.addTool({
  name: 'mysql_create_table',
  description: 'Create a new table with specified columns and constraints',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to create'),
    columns: z.array(
      z.object({
        name: z.string(),
        type: z.string(),
        nullable: z.boolean().optional(),
        default: z.string().optional(),
        primary_key: z.boolean().optional(),
        auto_increment: z.boolean().optional()
      })
    ).describe('Array of column definitions')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);

      const columnDefs: string[] = [];
      args.columns.forEach(col => {
        mysqlManager['validateInput'](col.name, 'column_name');
        mysqlManager['validateInput'](col.type, 'column_type');

        let definition = `\`${col.name}\` ${col.type}`;

        if (col.nullable === false) {
          definition += " NOT NULL";
        }
        if (col.auto_increment) {
          definition += " AUTO_INCREMENT";
        }
        if (col.default) {
          definition += ` DEFAULT ${col.default}`;
        }

        columnDefs.push(definition);
      });

      const primaryKeys = args.columns
        .filter(col => col.primary_key)
        .map(col => col.name);

      if (primaryKeys.length > 0) {
        columnDefs.push(`PRIMARY KEY (\`${primaryKeys.join('`, `')}\`)`);
      }

      const query = `CREATE TABLE \`${args.table_name}\` (${columnDefs.join(', ')})`;
      const result = await mysqlManager.executeQuery(query);

      // 表创建后使缓存失效
      mysqlManager.invalidateCaches("CREATE");

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_CREATE_TABLE_FAILED} ${error.message}`);
    }
  }
});

/**
 * 删除表工具
 *
 * 从数据库中删除（丢弃）表。
 * 支持 IF EXISTS 选项以避免表不存在时的错误。
 *
 * @tool mysql_drop_table
 * @param {string} table_name - 要删除的表名（经过安全验证）
 * @param {boolean} [if_exists] - 使用 IF EXISTS 子句以避免表不存在时的错误
 * @returns {Promise<string>} 包含成功状态和删除信息的 JSON 格式结果
 * @throws {Error} 当表名无效或删除失败时抛出
 */
mcp.addTool({
  name: 'mysql_drop_table',
  description: 'Drop (delete) a table from the database',
  parameters: z.object({
    table_name: z.string().describe('Name of the table to drop'),
    if_exists: z.boolean().optional().describe('Use IF EXISTS clause to avoid errors if table does not exist')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);

      const query = `DROP TABLE ${args.if_exists ? 'IF EXISTS' : ''} \`${args.table_name}\``;
      const result = await mysqlManager.executeQuery(query);

      // 表删除后使缓存失效
      mysqlManager.invalidateCaches("DROP");

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_DROP_TABLE_FAILED} ${error.message}`);
    }
  }
});

/**
 * 诊断连接工具
 *
 * 诊断 MySQL 连接状态和配置信息。
 * 提供连接池状态、性能指标和连接测试结果。
 *
 * @tool mysql_diagnose_connection
 * @returns {Promise<string>} 包含诊断信息的 JSON 格式结果
 * @throws {Error} 当诊断过程失败时抛出
 */
mcp.addTool({
  name: 'mysql_diagnose_connection',
  description: 'Diagnose MySQL connection status and configuration',
  parameters: z.object({}),
  execute: async () => {
    try {
      const diagnosis: Record<string, any> = {
        [StringConstants.FIELD_CONNECTION_POOL_STATUS]: mysqlManager['connectionPool'].getStats(),
        [StringConstants.FIELD_CONFIG]: mysqlManager['configManager'].toObject(),
        [StringConstants.FIELD_PERFORMANCE_METRICS]: mysqlManager.getPerformanceMetrics(),
        enhanced_metrics: mysqlManager['enhancedMetrics'].getComprehensiveMetrics()
      };

      // 执行简单的连接测试
      try {
        const connectionTestQuery = "SELECT 1 as test_connection";
        const testResult = await mysqlManager.executeQuery(connectionTestQuery);
        diagnosis[StringConstants.FIELD_CONNECTION_TEST] = {
          [StringConstants.STATUS_KEY]: StringConstants.STATUS_SUCCESS,
          [StringConstants.FIELD_RESULT]: testResult
        };
      } catch (error: any) {
        diagnosis[StringConstants.FIELD_CONNECTION_TEST] = {
          [StringConstants.STATUS_KEY]: StringConstants.STATUS_FAILED,
          [StringConstants.ERROR_KEY]: error.message
        };
      }

      return JSON.stringify(diagnosis, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_DIAGNOSE_FAILED} ${error.message}`);
    }
  }
});

/**
 * Signal Handlers for Graceful Shutdown
 *
 * Ensures proper cleanup of database connections, cache clearing, and resource
 * deallocation when the process is terminated by SIGINT or SIGTERM signals.
 * This prevents connection leaks and ensures data integrity.
 */
process.on('SIGINT', async () => {
  console.error(`\n${StringConstants.MSG_SIGNAL_RECEIVED} SIGINT, ${StringConstants.MSG_GRACEFUL_SHUTDOWN}`);
  await mysqlManager.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.error(`\n${StringConstants.MSG_SIGNAL_RECEIVED} SIGTERM, ${StringConstants.MSG_GRACEFUL_SHUTDOWN}`);
  await mysqlManager.close();
  process.exit(0);
});

/**
 * Module Exports
 *
 * Exports the configured MCP server instance and MySQL manager for external use,
 * testing, or integration with other modules.
 */
export { mcp, mysqlManager };

/**
 * Server Startup Function
 *
 * Initializes and starts the MySQL MCP server with error handling.
 * Ensures proper cleanup on startup failure.
 *
 * @async
 * @function startServer
 * @returns {Promise<void>} Promise that resolves when server starts successfully
 * @throws {Error} When server initialization fails
 */
export async function startServer(): Promise<void> {
  try {
    console.error(StringConstants.MSG_SERVER_RUNNING);
    await mcp.start();
  } catch (error: any) {
    console.error(`${StringConstants.MSG_SERVER_ERROR} ${error.message}`);
    await mysqlManager.close();
    process.exit(1);
  }
}

/**
 * Auto-start Server
 *
 * Automatically starts the server when this module is executed directly.
 * This enables the server to run as a standalone application.
 */
startServer();